
"""
pyinstaller --noconfirm --onefile --windowed --name "Cursor自动注册工具" --add-data "turnstilePatch;turnstilePatch" --collect-all DrissionPage --collect-all pymysql --collect-all requests --collect-all bs4 --collect-all chardet --collect-all dotenv --hidden-import win32console --hidden-import win32api Cursor自动注册.py

"""
import base64
import hashlib
import os
import random
import string
import sys
import threading
import time
import tkinter as tk
import uuid
from tkinter import ttk, messagebox, scrolledtext
import pymysql
import requests

# 添加额外的导入
import logging
from DrissionPage import ChromiumOptions, Chromium
from win32api import GetSystemMetrics

#外部模块
from 创建邮箱.创建邮箱 import EmailManager
from 接验证码.cloudflare_mail import get_verification_code


class BrowserManager:
    """浏览器管理器类，用于管理Chrome浏览器实例"""

    # 添加一个类级别的计数器和锁，用于管理窗口位置
    window_count = 0
    window_lock = threading.Lock()
    total_threads = 0  # 总线程数，用于动态调整窗口排列

    def __init__(self):
        self.browser = None
        # 为每个实例分配一个唯一的窗口ID
        with BrowserManager.window_lock:
            self.window_id = BrowserManager.window_count
            BrowserManager.window_count = (BrowserManager.window_count + 1) % 20  # 最多支持20个窗口，超过后重新开始

    def init_browser(self, user_agent=None):
        """初始化浏览器"""
        co = self._get_browser_options(user_agent)
        self.browser = Chromium(co)
        return self.browser

    def _get_browser_options(self, user_agent=None):
        """获取浏览器配置"""
        co = ChromiumOptions()
        try:
            # 加载防封插件
            extension_path = self._get_extension_path()
            co.add_extension(extension_path)
        except FileNotFoundError as e:
            logging.warning(f"警告: {e}")

        # 为每个浏览器实例设置不同的用户配置文件
        profile_name = f'Profile_{self.window_id}'
        co.set_user(user=profile_name)

        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")
        proxy = os.getenv("BROWSER_PROXY")
        if proxy:
            co.set_proxy(proxy)

        co.auto_port()
        if user_agent:
            co.set_user_agent(user_agent)

        # 获取屏幕尺寸
        try:
            screen_width = GetSystemMetrics(0)
            screen_height = GetSystemMetrics(1)

            # 根据总线程数动态计算窗口排列
            # 如果线程数 <= 10：每排5个，最多2排
            # 如果线程数 > 10：每排10个，最多2排
            if BrowserManager.total_threads <= 10:
                max_columns = 5
                max_rows = 2
            else:
                max_columns = 10
                max_rows = 2

            # 根据屏幕尺寸和窗口数量计算合适的窗口大小
            window_width = min(600, screen_width // max_columns)  # 每个窗口宽度不超过600
            window_height = min(600, screen_height // max_rows)   # 每个窗口高度不超过600

            # 设置浏览器窗口大小
            co.set_argument(f"--window-size={window_width},{window_height}")

            # 计算位置 - 根据线程数动态调整每排窗口数
            row = self.window_id // max_columns  # 行号
            col = self.window_id % max_columns   # 列号

            # 计算左上角坐标
            x_pos = col * window_width
            y_pos = row * window_height

            # 确保窗口不会超出屏幕
            if x_pos + window_width > screen_width:
                x_pos = screen_width - window_width
            if y_pos + window_height > screen_height:
                y_pos = screen_height - window_height

            # 设置窗口位置
            co.set_argument(f"--window-position={x_pos},{y_pos}")
        except Exception as e:
            # 出错时使用默认值
            co.set_argument("--window-size=600,600")
            co.set_argument("--window-position=0,0")

        # 启用 br 压缩编码支持
        co.set_pref("profile.default_content_encoding_list", ["br", "gzip", "deflate"])
        co.set_argument("--enable-features=BrotliEncoding")
        # 添加接受的内容编码头
        co.set_pref("webkit.webprefs.default_encoding", "UTF-8")

        # Mac 系统特殊处理
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")

        return co

    def _get_extension_path(self):
        """获取插件路径"""
        root_dir = os.getcwd()
        extension_path = os.path.join(root_dir, "turnstilePatch")

        if hasattr(sys, "_MEIPASS"):
            extension_path = os.path.join(sys._MEIPASS, "turnstilePatch")

        if not os.path.exists(extension_path):
            raise FileNotFoundError(f"插件不存在: {extension_path}")

        return extension_path

    def quit(self):
        """关闭浏览器"""
        if self.browser:
            try:
                self.browser.quit()
            except:
                pass


# 重定向stdout到日志框
class TextRedirector:
    def __init__(self, text_widget):
        self.text_space = text_widget
        self.buffer = ""

    def write(self, string):
        self.buffer += string
        if '\n' in self.buffer:
            self.text_space.insert(tk.END, self.buffer)
            self.text_space.see(tk.END)
            self.buffer = ""

    def flush(self):
        if self.buffer:
            self.text_space.insert(tk.END, self.buffer)
            self.text_space.see(tk.END)
            self.buffer = ""

class CursorRegistration:
    """Cursor注册自动化类"""

    def __init__(self, browser):
        """初始化注册器"""
        self.browser = browser
        self.first_name = None
        self.last_name = None
        self.email = None
        self.password = None
        self.verification_code = None
        # 状态跟踪：记录已使用过的检测条件，避免重复检测
        self.used_checks = set()

    def get_used_checks_status(self):
        """获取已使用检测条件的状态信息"""
        if not self.used_checks:
            return "无已使用的检测条件"
        return f"已使用的检测条件: {', '.join(sorted(self.used_checks))}"

    def handle_turnstile(self):
        """处理Turnstile人机验证"""
        print("检查人机验证状态...")
        print(f"当前检测状态: {self.get_used_checks_status()}")
        try:
            # 首先检查是否已经通过验证（页面已跳转到下一步）
            if self.check_verification_success():
                print("页面已跳转到下一步，人机验证已通过!")
                print(f"更新后检测状态: {self.get_used_checks_status()}")
                return True

            # 检查页面是否存在Turnstile验证框
            turnstile = self.browser.ele("@id=cf-turnstile", timeout=3)
            if not turnstile:
                print("未检测到Turnstile验证框")
                return True

            print("检测到Turnstile验证框，开始处理...")
            # 尝试重置验证
            self.browser.run_js("try { turnstile.reset() } catch(e) { }")
            time.sleep(2)

            # 定位验证框元素
            max_attempts = 2
            for attempt in range(max_attempts):
                try:
                    # 再次检查是否已经通过验证
                    if self.check_verification_success():
                        print("人机验证已通过!")
                        return True

                    # 尝试通过shadow DOM定位验证元素
                    challenge_check = (
                        self.browser.ele("@id=cf-turnstile", timeout=2)
                        .child()
                        .shadow_root.ele("tag:iframe")
                        .ele("tag:body")
                        .sr("tag:input")
                    )

                    if challenge_check:
                        print("找到验证框元素，点击...")
                        challenge_check.click()
                        time.sleep(2)

                        # 检查验证是否通过
                        if self.check_verification_success():
                            print("人机验证成功!")
                            return True
                except Exception as e:
                    print(f"尝试处理验证失败: {e}")

                # 检查验证是否已通过
                if self.check_verification_success():
                    print("人机验证成功!")
                    return True

                print(f"验证未通过，重试 {attempt+1}/{max_attempts}...")
                time.sleep(1)

            # 最后检查一次
            if self.check_verification_success():
                print("人机验证成功!")
                return True

            print("人机验证未通过")
            return False
        except Exception as e:
            print(f"处理人机验证出错: {e}")
            return False

    def check_verification_success(self):
        """检查验证是否通过"""
        try:
            # 检查是否有错误信息，如果有则验证失败
            error_messages = [
                'xpath://div[contains(text(), "Can\'t verify the user is human")]',
                'xpath://div[contains(text(), "Error: 600010")]',
                'xpath://div[contains(text(), "Please try again")]'
            ]

            for error_xpath in error_messages:
                if self.browser.ele(error_xpath, timeout=0.5):
                    return False

            # 1. 检查是否有密码输入框（表单提交后）
            # 如果这个检测条件已经使用过，跳过检测
            if "password_input" not in self.used_checks:
                if self.browser.ele("@name=password", timeout=0.5):
                    print("检测到密码输入框，标记此检测条件为已使用")
                    self.used_checks.add("password_input")
                    return True

            # 2. 检查是否有验证码输入框（密码提交后）
            # 如果这个检测条件已经使用过，跳过检测
            if "verification_code_input" not in self.used_checks:
                if self.browser.ele("@data-index=0", timeout=0.5):
                    print("检测到验证码输入框，标记此检测条件为已使用")
                    self.used_checks.add("verification_code_input")
                    return True

            # 3. 检查是否有账户设置页面（注册完成）
            # if (self.browser.ele("Account Settings", timeout=0.5) or
            #     self.browser.ele("@href='/account'", timeout=0.5)):
            #     return True

            return False
        except:
            return False

    def generate_password(self, length=12):
        """生成固定密码"""
        return "Qq44egWTg121515Q"

    def generate_name(self, length=6):
        """生成随机用户名"""
        return random.choice(string.ascii_uppercase) + "".join(random.choices(string.ascii_lowercase, k=length-1))

    def setup_account_info(self, email):
        """设置账号信息"""
        self.email = email
        self.password = self.generate_password()
        self.first_name = self.generate_name()
        self.last_name = self.generate_name()

    def fill_registration_form(self):
        """填写注册表单"""
        print("填写注册表单...")
        try:
            first_name_input = self.browser.ele("@name=first_name", timeout=10)
            if not first_name_input:
                print("找不到名字输入框")
                return False

            first_name_input.input(self.first_name)
            time.sleep(0.1)  # 减少等待时间

            self.browser.ele("@name=last_name").input(self.last_name)
            time.sleep(0.1)  # 减少等待时间

            self.browser.ele("@name=email").input(self.email)
            time.sleep(0.1)  # 减少等待时间

            self.browser.ele("@type=submit").click()
            time.sleep(3)  # 减少1秒等待时间
            return True
        except Exception as e:
            print(f"填写注册表单失败: {e}")
            return False

    def fill_password_form(self):
        """填写密码表单"""
        print("填写密码表单...")
        try:
            password_input = self.browser.ele("@name=password", timeout=20)  # 减少超时时间
            if not password_input:
                print("找不到密码输入框")
                return False

            password_input.input(self.password)
            time.sleep(0.1)  # 减少等待时间

            self.browser.ele("@type=submit").click()
            time.sleep(3)

            return True
        except Exception as e:
            print(f"填写密码表单失败: {e}")
            return False

    def wait_for_verification_page(self):
        """等待验证码输入页面出现"""
        print("等待验证码输入页面...")
        try:
            # 直接等待验证码输入框出现
            first_digit_input = self.browser.ele("@data-index=0", timeout=30)
            if first_digit_input:
                print("验证码输入页面已出现")
                return True
            else:
                print("验证码输入页面未出现")
                return False
        except Exception as e:
            print(f"等待验证码输入页面时出错: {e}")
            return False

    def enter_verification_code(self, code):
        """输入验证码"""
        print(f"输入验证码: {code}...")
        self.verification_code = code

        try:
            first_digit_input = self.browser.ele("@data-index=0", timeout=10)
            if not first_digit_input:
                print("找不到验证码输入框")
                return False

            # 输入验证码
            for i, digit in enumerate(code):
                self.browser.ele(f"@data-index={i}").input(digit)
                # 不需要等待，直接连续输入

            # 输入完成后查找并点击提交按钮
            submit_button = self.browser.ele("@type=submit", timeout=2) or \
                           self.browser.ele("button[type='submit']", timeout=1) or \
                           self.browser.ele("button:contains('验证')", timeout=1) or \
                           self.browser.ele("button:contains('确认')", timeout=1) or \
                           self.browser.ele("button.primary", timeout=1)

            if submit_button:
                print("点击验证码提交按钮...")
                submit_button.click()
                time.sleep(1)
            else:
                print("未找到提交按钮，继续等待...")

            return True
        except Exception as e:
            print(f"输入验证码失败: {e}")
            return False

    def verify_registration_success(self):
        """验证注册是否成功"""
        print("验证注册是否成功...")
        try:
            # 输入验证码后等待5秒，然后直接认为注册成功
            # print("等待5秒后直接跳转到下一个页面...")
            # time.sleep(5)

            # print(f"账号 {self.email} 注册成功！")
            # return True

            # 原来的代码（已屏蔽用于测试）
            # # 减少等待时间，确保页面加载
            # time.sleep(1)

            # account_settings = self.browser.ele("Account Settings", timeout=70) or self.browser.ele("@href='/account'", timeout=70)

            # if account_settings:
            #     print(f"账号 {self.email} 注册成功！")
            #     return True
            # else:
            #     print("无法确认注册是否成功")
            #     return False
            #延时5秒
            time.sleep(5)
            return True
        except Exception as e:
            print(f"验证注册失败: {e}")
            return False


    def generate_session_id(self):
        """生成26位的authorization_session_id"""
        # 使用观察到的字符集：数字和大写字母（排除容易混淆的字符）
        chars = "0123456789ABCDEFGHJKMNPQRSTVWXYZ"
        return ''.join(random.choice(chars) for _ in range(26))

    def generate_cursor_url(self):
        """生成Cursor认证URL"""
        base_url = "https://authenticator.cursor.sh/sign-up"

        # 固定参数（从原始URL中提取）
        # state = "%257B%2522returnTo%2522%253A%2522%252Fsettings%2522%257D"
        # redirect_uri = "https%3A%2F%2Fcursor.com%2Fapi%2Fauth%2Fcallback"

        # # 生成随机的session ID
        # session_id = self.generate_session_id()

        # # 组装完整URL
        # url = f"{base_url}?state={state}&redirect_uri={redirect_uri}&authorization_session_id={session_id}"

        return base_url

    def register(self, email):
        """执行完整注册流程"""
        print(f"开始注册Cursor账号: {email}")
        print(f"检测状态: {self.get_used_checks_status()}")

        # 设置账号信息
        self.setup_account_info(email)

        # 生成注册URL
        register_url = self.generate_cursor_url()
        print(f"使用动态生成的注册URL: {register_url}")

        # 打开注册页面
        self.browser.get(register_url)
        # time.sleep(3)  # 减少等待时间

        # 在页面加载后进行第一次人机验证处理
        # if not self.handle_turnstile():
        #     print("初始人机验证失败")
        #     return False

        # 填写注册表单
        if not self.fill_registration_form():
            return False

        # 表单提交后进行第二次人机验证处理
        if not self.handle_turnstile():
            print("表单提交后人机验证失败")
            return False

        # 填写密码表单
        if not self.fill_password_form():
            return False

        # 密码提交后进行第三次人机验证处理
        if not self.handle_turnstile():
            print("密码提交后人机验证失败")
            return False

        # 等待验证码输入页面出现
        if not self.wait_for_verification_page():
            return False

        return True

    def complete_registration(self, verification_code):
        """完成注册流程"""
        # 输入验证码
        if not self.enter_verification_code(verification_code):
            return False

        # 验证注册是否成功
        if not self.verify_registration_success():
            return False

        return True

class DatabaseManager:
    """数据库管理类，处理Cursor账号数据库操作"""

    # 数据库配置
    DB_CONFIGS = {
        "腾讯云": {
            "host": "***************",
            "port": 3306,
            "database": "kami3162",
            "user": "root",
            "password": "YU6709",
            "connect_timeout": 30,
            "charset": "utf8mb4"
        },
        "火山云": {
            "host": "**************",
            "port": 3306,
            "database": "kami3162",
            "user": "root",
            "password": "Yuyu6709.",
            "connect_timeout": 30,
            "charset": "utf8mb4"
        }
    }

    def __init__(self, db_choice="腾讯云"):
        """初始化数据库管理器"""
        self.config = self.DB_CONFIGS.get(db_choice, self.DB_CONFIGS["腾讯云"])

    def save_account(self, email, access_token, refresh_token):
        """保存账号信息到数据库"""
        print(f"保存账号 {email} 到数据库...")

        # 添加重试机制
        for retry in range(2):  # 最多重试2次
            if retry > 0:
                print(f"重试连接网络数据库...")

            conn = None
            cursor = None
            try:
                # 连接到MySQL数据库
                try:
                    print(f"尝试连接网络数据库: {self.config['host']}:{self.config['port']}")
                    conn = pymysql.connect(**self.config)
                    print("网络数据库连接成功")
                except Exception as e:
                    print(f"网络数据库连接失败: {e}")
                    time.sleep(1)
                    continue

                cursor = conn.cursor()

                # 检查邮箱是否已存在
                check_sql = "SELECT COUNT(*) FROM 邮箱系统 WHERE 邮箱 = %s"
                cursor.execute(check_sql, (email,))
                result = cursor.fetchone()

                if result and result[0] > 0:
                    return True

                # 插入新记录
                sql = "INSERT INTO 邮箱系统 (邮箱, 访问令牌, 刷新令牌, 创建时间) VALUES (%s, %s, %s, NOW())"
                values = (email, access_token, refresh_token)

                cursor.execute(sql, values)
                conn.commit()

                return True

            except pymysql.Error as e:
                # 特殊处理常见错误
                if hasattr(e, 'args') and len(e.args) > 1:
                    if e.args[0] == 1142:  # 权限错误
                        print("数据库权限错误")
                    elif e.args[0] == 1062:  # 重复键错误
                        return True

                # 最后一次尝试失败时才输出错误
                if retry == 1:
                    print(f"网络数据库错误: {e}")
            except Exception as e:
                if retry == 1:
                    print(f"网络连接错误: {e}")
            finally:
                if cursor:
                    cursor.close()
                if conn:
                    conn.close()

            # 尝试之间添加延迟
            time.sleep(1)

        return False

class CursorPostRegistration:
    """处理Cursor注册成功后的操作类"""

    def __init__(self, browser):
        """初始化后处理器"""
        self.browser = browser
        self.email = None
        self.access_token = None
        self.refresh_token = None
        # 状态跟踪：记录已使用过的检测条件，避免重复检测
        self.used_checks = set()

    def get_used_checks_status(self):
        """获取已使用检测条件的状态信息"""
        if not self.used_checks:
            return "无已使用的检测条件"
        return f"已使用的检测条件: {', '.join(sorted(self.used_checks))}"

    def generate_auth_params(self):
        """生成认证参数"""
        # 1. 生成 code_verifier (t) - 32字节随机数
        t = os.urandom(32)  # 等效于 JS 的 crypto.getRandomValues(new Uint8Array(32))

        # 2. 生成 s: 对 t 进行 Base64 URL 安全编码
        def tb(data):
            # Base64 URL 安全编码（替换 +/ 为 -_，去除末尾的 =）
            return base64.urlsafe_b64encode(data).decode().rstrip('=')

        s = tb(t)  # 对应 JS 的 this.tb(t)

        # 3. 生成 n: 对 s 进行 SHA-256 哈希 + Base64 URL 编码
        def ub(s_str):
            # 等效于 JS 的 TextEncoder().encode(s) + SHA-256
            return hashlib.sha256(s_str.encode()).digest()

        hashed = ub(s)
        n = tb(hashed)  # 对应 JS 的 this.tb(new Uint8Array(hashed))

        # 4. 生成 r: UUID v4
        r = str(uuid.uuid4())  # 对应 JS 的 $t()

        return {
            "t": t.hex(),      # 原始字节转十六进制字符串（方便查看）
            "s": s,
            "n": n,
            "r": r
        }

    def poll_for_login_result(self, uuid, challenge):
        """轮询获取登录结果"""
        poll_url = f"https://api2.cursor.sh/auth/poll?uuid={uuid}&verifier={challenge}"
        headers = {
            "Content-Type": "application/json"
        }
        max_attempts = 30
        attempt = 0

        while attempt < max_attempts:
            print("正在获取登录结果...")
            try:
                response = requests.get(poll_url, headers=headers)

                if response.status_code == 404:
                    print("登录尚未完成.")
                elif response.status_code == 200:
                    data = response.json()

                    if "authId" in data and "accessToken" in data and "refreshToken" in data:
                        print("登录成功!")
                        # 不再打印令牌详情
                        return data['authId'], data['accessToken'], data['refreshToken']

            except Exception as e:
                print(f"轮询过程中发生错误: {e}")

            attempt += 1
            time.sleep(0.5)  # 每0.5秒轮询一次，加快速度

        if attempt >= max_attempts:
            print("轮询超时.")
            return None, None, None

    def process(self, email):
        """执行完整的后处理流程"""
        self.email = email

        # 生成认证参数
        params = self.generate_auth_params()

        # 构建URL
        url = f"https://www.cursor.com/cn/loginDeepControl?challenge={params['n']}&uuid={params['r']}&mode=login"

        # 访问URL
        self.browser.get(url)

        # 减少页面加载等待时间
        time.sleep(1)

        # 查找并点击"登录"按钮
        try:
            print("点击登录按钮...")
            button_clicked = self.browser.run_js("""
                try {
                    const button = document.querySelectorAll(".min-h-screen")[1].querySelectorAll(".gap-4")[1].querySelectorAll("button")[1];
                    if (button) {
                        button.click();
                        return true;
                    } else {
                        return false;
                    }
                } catch (e) {
                    console.error("选择器错误:", e);
                    return false;
                }
            """)

            if not button_clicked:
                print("未找到登录按钮，尝试再次查找...")
                # 再次尝试点击按钮
                self.browser.run_js("""
                    try {
                        const button = document.querySelectorAll(".min-h-screen")[1].querySelectorAll(".gap-4")[1].querySelectorAll("button")[1];
                        if (button) {
                            button.click();
                            return true;
                        } else {
                            return false;
                        }
                    } catch (e) {
                        console.error("选择器错误:", e);
                        return false;
                    }
                """)

            # 减少按钮点击响应等待时间
            time.sleep(1)

            # 处理可能出现的人机验证
            self.handle_turnstile()

            # 轮询获取登录结果
            auth_id, access_token, refresh_token = self.poll_for_login_result(params["r"], params["s"])

            if access_token and refresh_token:
                # 保存账户信息到数据库
                password = "Qq44egWTg121515Q"  # 使用固定密码

                # 使用全局变量中的数据库选择
                db_choice = getattr(self.browser, 'db_choice', "腾讯云")
                db_manager = DatabaseManager(db_choice)

                if db_manager.save_account(email, access_token, refresh_token):
                    print("\n" + "="*30)
                    print(f"📁 账号保存成功: {email}")
                    print(f"📊 保存到数据库: {db_choice}")
                    print("="*30 + "\n")
                    return True
                else:
                    print("保存账号失败")
                    return False
            else:
                print("获取令牌失败")
                return False

        except Exception as e:
            print(f"处理账号时出错: {e}")
            return False

    def handle_turnstile(self):
        """处理Turnstile人机验证"""
        print("检查人机验证状态...")
        print(f"当前检测状态: {self.get_used_checks_status()}")
        try:
            # 首先检查是否已经通过验证（页面已跳转到下一步）
            if self.check_verification_success():
                print("页面已跳转到下一步，人机验证已通过!")
                print(f"更新后检测状态: {self.get_used_checks_status()}")
                return True

            # 检查页面是否存在Turnstile验证框
            turnstile = self.browser.ele("@id=cf-turnstile", timeout=3)
            if not turnstile:
                print("未检测到Turnstile验证框")
                return True

            print("检测到Turnstile验证框，开始处理...")
            # 尝试重置验证
            self.browser.run_js("try { turnstile.reset() } catch(e) { }")
            time.sleep(2)

            # 定位验证框元素
            max_attempts = 2
            for attempt in range(max_attempts):
                try:
                    # 再次检查是否已经通过验证
                    if self.check_verification_success():
                        print("人机验证已通过!")
                        return True

                    # 尝试通过shadow DOM定位验证元素
                    challenge_check = (
                        self.browser.ele("@id=cf-turnstile", timeout=2)
                        .child()
                        .shadow_root.ele("tag:iframe")
                        .ele("tag:body")
                        .sr("tag:input")
                    )

                    if challenge_check:
                        print("找到验证框元素，点击...")
                        challenge_check.click()
                        time.sleep(2)

                        # 检查验证是否通过
                        if self.check_verification_success():
                            print("人机验证成功!")
                            return True
                except Exception as e:
                    print(f"尝试处理验证失败: {e}")

                # 检查验证是否已通过
                if self.check_verification_success():
                    print("人机验证成功!")
                    return True

                print(f"验证未通过，重试 {attempt+1}/{max_attempts}...")
                time.sleep(1)

            # 最后检查一次
            if self.check_verification_success():
                print("人机验证成功!")
                return True

            print("人机验证未通过")
            return False
        except Exception as e:
            print(f"处理人机验证出错: {e}")
            return False

    def check_verification_success(self):
        """检查验证是否通过"""
        try:
            # 检查是否有错误信息，如果有则验证失败
            error_messages = [
                'xpath://div[contains(text(), "Can\'t verify the user is human")]',
                'xpath://div[contains(text(), "Error: 600010")]',
                'xpath://div[contains(text(), "Please try again")]'
            ]

            for error_xpath in error_messages:
                if self.browser.ele(error_xpath, timeout=0.5):
                    return False

            # 检查页面上是否出现了下一步的表单元素，表示验证已通过
            # 1. 检查是否有姓名输入框（初始页面）
            # 如果这个检测条件已经使用过，跳过检测
            if "first_name_input" not in self.used_checks:
                if self.browser.ele("@name=first_name", timeout=0.5):
                    print("检测到姓名输入框，标记此检测条件为已使用")
                    self.used_checks.add("first_name_input")
                    return True

            # 2. 检查是否有密码输入框（表单提交后）
            # 如果这个检测条件已经使用过，跳过检测
            if "password_input" not in self.used_checks:
                if self.browser.ele("@name=password", timeout=0.5):
                    print("检测到密码输入框，标记此检测条件为已使用")
                    self.used_checks.add("password_input")
                    return True

            # 3. 检查是否有验证码输入框（密码提交后）
            # 如果这个检测条件已经使用过，跳过检测
            if "verification_code_input" not in self.used_checks:
                if self.browser.ele("@data-index=0", timeout=0.5):
                    print("检测到验证码输入框，标记此检测条件为已使用")
                    self.used_checks.add("verification_code_input")
                    return True

            # 4. 检查是否有账户设置页面（注册完成）
            # 如果这个检测条件已经使用过，跳过检测
            if "account_settings" not in self.used_checks:
                if (self.browser.ele("Account Settings", timeout=0.5) or
                    self.browser.ele("@href='/account'", timeout=0.5)):
                    print("检测到账户设置页面，标记此检测条件为已使用")
                    self.used_checks.add("account_settings")
                    return True

            return False
        except:
            return False

class RegisterThread(threading.Thread):
    """Cursor注册线程类"""

    def __init__(self, thread_id, success_counter, db_lock, db_choice="腾讯云"):
        """初始化线程"""
        super().__init__()
        self.thread_id = thread_id
        self.success_counter = success_counter
        self.db_lock = db_lock
        self.running = True
        self.db_choice = db_choice  # 使用传入的数据库选择

    def log(self, message):
        """打印带有线程ID的日志"""
        print(f"[线程 {self.thread_id}] {message}")

    def run(self):
        """线程运行函数"""
        self.log(f"线程启动")

        # 创建邮箱管理器
        email_manager = EmailManager()

        # 创建浏览器管理器
        browser_manager = BrowserManager()

        try:
            while self.running:
                browser = None
                try:
                    # 初始化新的浏览器实例
                    browser = browser_manager.init_browser()

                    # 将数据库选择设置为浏览器属性
                    setattr(browser.latest_tab, 'db_choice', self.db_choice)

                    # 初始化Cursor注册器和后处理器
                    registration = CursorRegistration(browser.latest_tab)
                    post_registration = CursorPostRegistration(browser.latest_tab)

                    # 获取邮箱
                    email = email_manager.get_email()
                    
                    # 检查是否还有可用的邮箱
                    if email is None:
                        self.log("数据库中没有可用的邮箱地址，停止注册")
                        self.running = False
                        break

                    # 执行注册流程
                    if not registration.register(email):
                        self.log("注册流程失败，重试...")
                        continue

                    # 到达验证码页面后获取验证码
                    self.log("正在获取验证码...")
                    # 使用cloudflare_mail模块获取验证码
                    verification_code = get_verification_code(email)
                    if not verification_code:
                        self.log("获取验证码失败，重试...")
                        continue

                    # 完成注册
                    success = registration.complete_registration(verification_code)

                    if success:
                        self.log(f"成功注册: {email}")

                        # 执行注册后处理
                        post_result = post_registration.process(email)
                        if post_result:
                            self.log(f"账号处理完成")
                            # 使用锁更新成功计数
                            with self.success_counter.get_lock():
                                self.success_counter.value += 1
                        else:
                            self.log(f"账号处理失败")
                    else:
                        self.log("注册失败，重试...")

                except Exception as e:
                    self.log(f"发生错误: {e}")

                finally:
                    # 无论注册成功与否，都关闭当前浏览器实例
                    if browser:
                        try:
                            browser_manager.quit()
                        except:
                            pass
                    time.sleep(1)  # 确保浏览器完全关闭

                # 循环间隔
                time.sleep(0.5)

        except KeyboardInterrupt:
            self.log("线程停止")
        finally:
            try:
                browser_manager.quit()
            except:
                pass
            self.log("线程结束")

    def stop(self):
        """停止线程"""
        self.running = False

class CursorRegisterApp:
    """Cursor注册应用主界面"""

    def __init__(self, root):
        self.root = root
        self.root.title("Cursor编程研究学习")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 可用的数据库选项
        self.db_choices = list(DatabaseManager.DB_CONFIGS.keys())

        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建控制区域
        self.create_control_area()

        # 创建日志区域
        self.create_log_area()

        # 注册线程和状态变量
        self.register_threads = []
        self.is_running = False
        self.success_count = 0
        self.db_lock = threading.Lock()

        # 初始化完成后重定向stdout和stderr
        sys.stdout = TextRedirector(self.log_text)
        sys.stderr = TextRedirector(self.log_text)

    def create_control_area(self):
        """创建控制区域"""
        control_frame = ttk.LabelFrame(self.main_frame, text="控制面板", padding="10")
        control_frame.pack(fill=tk.X, pady=5)

        # 显示邮箱信息
        info_frame = ttk.Frame(control_frame)
        info_frame.pack(fill=tk.X, pady=5)

        # 创建数据库选择区域
        db_frame = ttk.Frame(control_frame)
        db_frame.pack(fill=tk.X, pady=5)

        ttk.Label(db_frame, text="选择数据库:").pack(side=tk.LEFT, padx=5)
        self.db_var = tk.StringVar(value=self.db_choices[0] if self.db_choices else "数据库1")

        # 创建数据库下拉框
        self.db_combobox = ttk.Combobox(
            db_frame,
            textvariable=self.db_var,
            values=self.db_choices,
            width=20,
            state="readonly"
        )
        self.db_combobox.pack(side=tk.LEFT, padx=5)
        self.db_combobox.current(0)  # 设置默认选择第一个

        # 创建线程数量选择区域
        thread_frame = ttk.Frame(control_frame)
        thread_frame.pack(fill=tk.X, pady=5)

        ttk.Label(thread_frame, text="线程数量:").pack(side=tk.LEFT, padx=5)
        self.thread_var = tk.IntVar(value=1)  # 默认设置为1个线程
        thread_spinbox = ttk.Spinbox(thread_frame, from_=1, to=20, textvariable=self.thread_var, width=5)  # 增加最大线程数到20
        thread_spinbox.pack(side=tk.LEFT, padx=5)

        # 创建按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=10)

        self.start_button = ttk.Button(button_frame, text="开始注册", command=self.start_registration)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(button_frame, text="停止注册", command=self.stop_registration, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        self.clear_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT, padx=5)

        # 创建状态区域
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X, pady=5)

        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT, padx=5)
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)

        ttk.Label(status_frame, text="成功注册:").pack(side=tk.LEFT, padx=5)
        self.success_var = tk.StringVar(value="0")
        ttk.Label(status_frame, textvariable=self.success_var).pack(side=tk.LEFT, padx=5)

    def create_log_area(self):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(self.main_frame, text="运行日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def clear_log(self):
        """清空日志区域"""
        self.log_text.delete(1.0, tk.END)
        print("日志已清空")

    def start_registration(self):
        """开始注册过程"""
        if self.is_running:
            return

        thread_count = self.thread_var.get()
        if thread_count < 1 or thread_count > 20:
            messagebox.showerror("错误", "线程数量必须在1到20之间")
            return

        # 获取所选数据库
        db_choice = self.db_var.get().strip()
        if not db_choice:
            db_choice = "腾讯云"  # 默认使用腾讯云数据库

        # 更新UI状态
        self.status_var.set("正在运行")
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        self.is_running = True
        self.success_count = 0
        self.success_var.set("0")

        # 设置总线程数到浏览器管理器，用于动态调整窗口排列
        BrowserManager.total_threads = thread_count

        # 创建共享计数器记录成功注册数量
        try:
            import multiprocessing
            success_counter = multiprocessing.Value('i', 0)
        except:
            # 如果没有multiprocessing，使用简单的计数器
            class Counter:
                def __init__(self, initial=0):
                    self._value = initial
                    self._lock = threading.Lock()

                @property
                def value(self):
                    return self._value

                @value.setter
                def value(self, v):
                    self._value = v

                def get_lock(self):
                    return self._lock

            success_counter = Counter()

        # 创建并启动线程
        self.register_threads = []
        print(f"启动 {thread_count} 个线程:")
        for i in range(thread_count):
            print(f"  线程 {i+1} 启动中...")

            thread = RegisterThread(i+1, success_counter, self.db_lock, db_choice=db_choice)
            thread.daemon = True  # 设置为守护线程，主线程结束时自动结束
            self.register_threads.append(thread)
            thread.start()
            time.sleep(0.5)  # 错开线程启动时间以减少资源竞争

        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_threads, args=(success_counter,))
        monitor_thread.daemon = True
        monitor_thread.start()

    def stop_registration(self):
        """停止注册过程"""
        if not self.is_running:
            return

        print("\n正在停止注册线程...")
        self.is_running = False

        # 通知所有线程停止
        for thread in self.register_threads:
            thread.running = False

        # 清空注册线程列表
        self.register_threads = []

        # 重置浏览器窗口计数器位置
        BrowserManager.window_count = 0
        print("浏览器窗口位置已重置")

        # 更新UI状态
        self.status_var.set("已停止")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

        print("所有注册线程已停止，可以重新开始注册")

    def monitor_threads(self, success_counter):
        """监控线程并更新UI"""
        last_update_time = 0
        last_status = ""

        while self.is_running:
            # 每隔2秒才更新状态
            current_time = time.time()
            if current_time - last_update_time < 2:
                time.sleep(0.5)
                continue

            last_update_time = current_time

            # 检查线程是否都结束了
            active_count = sum(1 for t in self.register_threads if t.is_alive())

            # 更新UI
            status_text = f"运行中 - {active_count}/{len(self.register_threads)} 线程活动"
            self.status_var.set(status_text)

            # 确保成功计数显示为数字
            try:
                current_success = success_counter.value
            except (AttributeError, TypeError):
                current_success = 0

            self.success_var.set(str(current_success))

            # 生成状态信息
            status_msg = f"活动线程: {active_count}/{len(self.register_threads)} | 成功注册: {current_success} 个账号"

            # 只有状态变化时才输出
            if status_msg != last_status:
                print(f"\n{status_msg}")
                last_status = status_msg

            # 如果所有线程都已结束，退出循环
            if active_count == 0:
                print("\n所有线程已结束")
                self.stop_registration()

                # 使用after方法在主线程中安全显示消息框，避免阻塞
                self.root.after(100, lambda: self.show_completion_message(current_success))
                break

            time.sleep(0.5)

    def show_completion_message(self, success_count):
        """安全地显示完成消息"""
        messagebox.showinfo("完成", f"所有线程已结束，成功注册 {success_count} 个账号")

def main():
    """命令行主函数"""
    print("="*60)
    print("Cursor 自动注册系统 - 多线程版")
    print("="*60)

    # 可用的数据库选项
    db_choices = list(DatabaseManager.DB_CONFIGS.keys())

    # 让用户选择数据库
    print("\n可用的数据库:")
    for i, db in enumerate(db_choices, 1):
        print(f"{i}. {db}")

    try:
        db_choice = int(input("请选择数据库 (输入序号): "))
        if 1 <= db_choice <= len(db_choices):
            db_choice = db_choices[db_choice-1]
        else:
            db_choice = db_choices[0]
            print(f"无效选择，使用默认数据库: {db_choice}")
    except ValueError:
        db_choice = db_choices[0]
        print(f"无效输入，使用默认数据库: {db_choice}")

    # 询问要启动的线程数
    while True:
        try:
            thread_count = int(input("请输入要启动的线程数量 (1-20): "))
            if 1 <= thread_count <= 20:
                break
            print("请输入1到20之间的数字")
        except ValueError:
            print("请输入有效的数字")

    print(f"正在启动 {thread_count} 个注册线程，数据库: {db_choice}")

    # 设置总线程数到浏览器管理器，用于动态调整窗口排列
    BrowserManager.total_threads = thread_count

    # 创建共享的互斥锁用于数据库访问
    db_lock = threading.Lock()

    # 创建共享计数器记录成功注册数量
    try:
        import multiprocessing
        success_counter = multiprocessing.Value('i', 0)
    except:
        # 如果没有multiprocessing，使用简单的计数器
        class Counter:
            def __init__(self, initial=0):
                self._value = initial
                self._lock = threading.Lock()

            @property
            def value(self):
                return self._value

            @value.setter
            def value(self, v):
                self._value = v

            def get_lock(self):
                return self._lock

        success_counter = Counter()

    # 创建线程列表
    threads = []
    for i in range(thread_count):
        thread = RegisterThread(i+1, success_counter, db_lock, db_choice=db_choice)
        threads.append(thread)
        thread.daemon = True  # 设置为守护线程，主线程结束时自动结束
        thread.start()
        # 错开线程启动时间，避免资源竞争
        time.sleep(2)

    # 监控线程并显示统计信息
    try:
        last_update_time = 0
        last_status = ""

        while True:
            # 每隔2秒才更新状态
            current_time = time.time()
            if current_time - last_update_time < 2:
                time.sleep(0.5)
                continue

            last_update_time = current_time

            active_count = sum(1 for t in threads if t.is_alive())

            # 确保成功计数显示为数字
            try:
                current_success = success_counter.value
            except (AttributeError, TypeError):
                current_success = 0

            # 生成状态信息
            status_msg = f"活动线程: {active_count}/{thread_count} | 成功注册: {current_success} 个账号"

            # 只有状态变化时才输出
            if status_msg != last_status:
                print(f"\n{status_msg}")
                last_status = status_msg

            # 如果所有线程都已结束，退出循环
            if active_count == 0:
                print("\n所有线程已结束")
                break

            time.sleep(0.5)

    except KeyboardInterrupt:
        print("\n\n程序停止中...")
        # 通知所有线程停止
        for thread in threads:
            thread.stop()

        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=5)

    print("\n程序已结束")
    print(f"成功注册账号: {success_counter.value} 个")

def main_gui():
    """GUI主函数"""
    # 设置GUI模式标记
    setattr(sys, 'gui_mode', True)

    root = tk.Tk()
    app = CursorRegisterApp(root)
    root.mainloop()

if __name__ == "__main__":
    # 检查是否有命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--console":
        # 命令行模式
        main()
    else:
        # GUI模式
        main_gui()
