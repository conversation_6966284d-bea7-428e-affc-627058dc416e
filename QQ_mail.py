"""
QQ邮箱接码工具
用于获取指定邮箱地址的验证码
直接运行此文件即可获取验证码
指定收件人邮箱地址:icloud隐藏邮箱地址
"""

import imaplib
import email
from email.header import decode_header
import re
import time


def decode_str(s):
    """解码邮件头字符串"""
    value, charset = decode_header(s)[0]
    if charset and isinstance(value, bytes):
        return value.decode(charset)
    return str(value) if value else ""


def extract_verification_code(content):
    """提取6位数字验证码（支持空格分隔）"""
    # 匹配带空格的6位数字
    pattern = r'\d\s\d\s\d\s\d\s\d\s\d'
    match = re.search(pattern, content)
    if match:
        return match.group(0).replace(" ", "")
    
    # 匹配连续的6位数字
    pattern = r'\b\d{6}\b'
    match = re.search(pattern, content)
    if match:
        return match.group(0)
    
    return None


def get_email_content(msg):
    """获取邮件内容（优先纯文本）"""
    def decode_payload(part):
        body = part.get_payload(decode=True)
        charset = part.get_content_charset() or 'utf-8'
        try:
            return body.decode(charset)
        except (UnicodeDecodeError, AttributeError):
            return body.decode('utf-8', errors='replace') if body else ""

    # 尝试获取纯文本内容
    for part in msg.walk():
        if part.get_content_type() == "text/plain":
            content = decode_payload(part)
            if content.strip():
                return content

    # 如果没有纯文本，获取HTML内容
    for part in msg.walk():
        if part.get_content_type() == "text/html":
            content = decode_payload(part)
            if content.strip():
                return content

    return "无法获取邮件内容"


def get_verification_code(recipient_email):
    """获取指定邮箱的验证码

    Args:
        recipient_email: 收件人邮箱地址

    Returns:
        成功返回验证码，失败返回None
    """
    # 硬编码的QQ邮箱信息
    qq_email = "<EMAIL>"  # 请替换为你的QQ邮箱
    qq_password = "diqeoqnldnxjdaaf"  # 请替换为你的QQ邮箱授权码
    max_attempts = 50

    print(f"🔍 开始获取验证码，最多尝试 {max_attempts} 次...")

    try:
        # 连接QQ邮箱
        mail = imaplib.IMAP4_SSL("imap.qq.com", 993)
        mail.login(qq_email, qq_password)
        mail.select('INBOX')
        print(f"✅ 成功登录到 QQ邮箱 ({qq_email})")

        # 尝试获取验证码
        for attempt in range(max_attempts):
            # 搜索发送给指定收件人的邮件（不区分已读未读）
            status, data = mail.search(None, f'(TO "{recipient_email}")')
            if status != 'OK' or not data[0]:
                # 如果没找到，也尝试搜索包含收件人邮箱的邮件
                status, data = mail.search(None, f'(BODY "{recipient_email}")')
                if status != 'OK' or not data[0]:
                    print(f"⏳ 第 {attempt+1}/{max_attempts} 次尝试：未找到发送给 {recipient_email} 的邮件")
                    if attempt < max_attempts - 1:
                        time.sleep(1)  # 等待1秒再试
                    continue

            # 获取最新的邮件
            email_ids = data[0].split()
            latest_id = email_ids[-1]

            status, data = mail.fetch(latest_id, '(RFC822)')
            if status != 'OK':
                continue

            # 解析邮件
            msg = email.message_from_bytes(data[0][1])
            subject = decode_str(msg.get("Subject", ""))
            to_header = decode_str(msg.get("To", ""))
            content = get_email_content(msg)

            print(f"📧 找到相关邮件: {subject}")
            print(f"📮 收件人: {to_header}")

            # 提取验证码
            code = extract_verification_code(content)
            if code:
                print(f"🎯 提取到验证码: {code}")
                return code
            else:
                print(f"⚠️ 未能从邮件中提取验证码")
                print(f"📄 邮件内容预览: {content[:300]}...")
                # 继续尝试下一次

        print("❌ 验证码获取失败，已达到最大尝试次数")
        return None
            
    except Exception as e:
        print(f"❌ 获取验证码失败: {e}")
        return None
    finally:
        try:
            mail.close()
            mail.logout()
        except:
            pass


def check_all_emails():
    """检查所有邮件并尝试提取验证码"""
    qq_email = "<EMAIL>"
    qq_password = "diqeoqnldnxjdaaf"

    try:
        mail = imaplib.IMAP4_SSL("imap.qq.com", 993)
        mail.login(qq_email, qq_password)
        mail.select('INBOX')
        print(f"✅ 成功登录到 QQ邮箱 ({qq_email})")

        # 获取最近的邮件（最新50封）
        status, data = mail.search(None, 'ALL')
        if status != 'OK' or not data[0]:
            print("📭 没有邮件")
            return

        email_ids = data[0].split()
        # 只检查最新的50封邮件，避免处理太多
        recent_ids = email_ids[-50:] if len(email_ids) > 50 else email_ids
        print(f"📬 检查最近 {len(recent_ids)} 封邮件中的验证码...")

        found_codes = []
        for email_id in recent_ids:
            try:
                status, data = mail.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    continue

                msg = email.message_from_bytes(data[0][1])
                subject = decode_str(msg.get("Subject", ""))
                to_header = decode_str(msg.get("To", ""))
                content = get_email_content(msg)

                # 尝试提取验证码
                code = extract_verification_code(content)
                if code:
                    found_codes.append({
                        'code': code,
                        'subject': subject,
                        'to': to_header,
                        'content': content[:200]
                    })
                    print(f"🎯 在邮件中找到验证码: {code}")
                    print(f"📧 邮件主题: {subject}")
                    print(f"📮 收件人: {to_header}")
                    print(f"📄 邮件内容预览: {content[:200]}...")
                    print("-" * 50)

            except Exception as e:
                print(f"⚠️ 处理邮件 {email_id.decode()} 时出错: {e}")
                continue

        if found_codes:
            print(f"\n📊 总共找到 {len(found_codes)} 个验证码")
        else:
            print("\n❌ 未找到任何验证码")

    except Exception as e:
        print(f"❌ 检查邮件失败: {e}")
    finally:
        try:
            mail.close()
            mail.logout()
        except:
            pass


if __name__ == "__main__":
    # 在这里设置你需要获取验证码的目标邮箱
    target_email = "<EMAIL>"  # 修改为你需要接收验证码的邮箱

    print("=" * 50)
    print(f"🎯 目标邮箱: {target_email}")
    print("-" * 50)

    # 提供两种模式选择
    print("请选择运行模式:")
    print("1. 搜索指定邮箱的验证码")
    print("2. 检查所有邮件中的验证码")

    try:
        choice = input("请输入选择 (1 或 2): ").strip()

        if choice == "2":
            print("\n🔍 检查所有邮件中的验证码...")
            check_all_emails()
        else:
            print(f"\n🔍 搜索发送给 {target_email} 的验证码...")
            code = get_verification_code(target_email)
            if code:
                print(f"✅ 验证码: {code}")
            else:
                print("❌ 未获取到验证码")

    except KeyboardInterrupt:
        print("⚠️ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")

    print("=" * 50)
