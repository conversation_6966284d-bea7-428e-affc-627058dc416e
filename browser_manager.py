import logging
import os
import sys
import threading

from DrissionPage import ChromiumOptions, Chromium
from win32api import GetSystemMetrics


class BrowserManager:
    """浏览器管理器类，用于管理Chrome浏览器实例"""
    
    # 添加一个类级别的计数器和锁，用于管理窗口位置
    window_count = 0
    window_lock = threading.Lock()
    total_threads = 0  # 总线程数，用于动态调整窗口排列
    
    def __init__(self):
        self.browser = None
        # 为每个实例分配一个唯一的窗口ID
        with BrowserManager.window_lock:
            self.window_id = BrowserManager.window_count
            BrowserManager.window_count = (BrowserManager.window_count + 1) % 20  # 最多支持20个窗口，超过后重新开始

    def init_browser(self, user_agent=None):
        """初始化浏览器"""
        co = self._get_browser_options(user_agent)
        self.browser = Chromium(co)
        return self.browser

    def _get_browser_options(self, user_agent=None):
        """获取浏览器配置"""
        co = ChromiumOptions()
        try:
            # 加载防封插件
            extension_path = self._get_extension_path()
            co.add_extension(extension_path)
        except FileNotFoundError as e:
            logging.warning(f"警告: {e}")

        # 为每个浏览器实例设置不同的用户配置文件
        profile_name = f'Profile_{self.window_id}'
        co.set_user(user=profile_name)

        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")
        proxy = os.getenv("BROWSER_PROXY")
        if proxy:
            co.set_proxy(proxy)

        co.auto_port()
        if user_agent:
            co.set_user_agent(user_agent)
            
        # 获取屏幕尺寸
        try:
            screen_width = GetSystemMetrics(0)
            screen_height = GetSystemMetrics(1)
            
            # 根据总线程数动态计算窗口排列
            # 如果线程数 <= 10：每排5个，最多2排
            # 如果线程数 > 10：每排10个，最多2排
            if BrowserManager.total_threads <= 10:
                max_columns = 5
                max_rows = 2
            else:
                max_columns = 10
                max_rows = 2
            
            # 根据屏幕尺寸和窗口数量计算合适的窗口大小
            window_width = min(600, screen_width // max_columns)  # 每个窗口宽度不超过600
            window_height = min(600, screen_height // max_rows)   # 每个窗口高度不超过600
            
            # 设置浏览器窗口大小
            co.set_argument(f"--window-size={window_width},{window_height}")
            
            # 计算位置 - 根据线程数动态调整每排窗口数
            row = self.window_id // max_columns  # 行号
            col = self.window_id % max_columns   # 列号
            
            # 计算左上角坐标
            x_pos = col * window_width
            y_pos = row * window_height
            
            # 确保窗口不会超出屏幕
            if x_pos + window_width > screen_width:
                x_pos = screen_width - window_width
            if y_pos + window_height > screen_height:
                y_pos = screen_height - window_height
            
            # 设置窗口位置
            co.set_argument(f"--window-position={x_pos},{y_pos}")
        except Exception as e:
            # 出错时使用默认值
            co.set_argument("--window-size=600,600")
            co.set_argument("--window-position=0,0")

        # 启用 br 压缩编码支持
        co.set_pref("profile.default_content_encoding_list", ["br", "gzip", "deflate"])
        co.set_argument("--enable-features=BrotliEncoding")
        # 添加接受的内容编码头
        co.set_pref("webkit.webprefs.default_encoding", "UTF-8")

        # Mac 系统特殊处理
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")

        return co

    def _get_extension_path(self):
        """获取插件路径"""
        root_dir = os.getcwd()
        extension_path = os.path.join(root_dir, "turnstilePatch")

        if hasattr(sys, "_MEIPASS"):
            extension_path = os.path.join(sys._MEIPASS, "turnstilePatch")

        if not os.path.exists(extension_path):
            raise FileNotFoundError(f"插件不存在: {extension_path}")
        
        return extension_path

    def quit(self):
        """关闭浏览器"""
        if self.browser:
            try:
                self.browser.quit()
            except:
                pass 